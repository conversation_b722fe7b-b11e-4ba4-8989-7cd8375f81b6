{"name": "mcp-server-blinko", "version": "0.0.8", "description": "MCP server for interacting with Blinko note service", "private": false, "type": "module", "homepage": "https://github.com/BryceWG/mcp-server-blinko", "bin": {"mcp-server-blinko": "build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0"}, "devDependencies": {"@types/node": "^20.17.28", "typescript": "^5.3.3"}}